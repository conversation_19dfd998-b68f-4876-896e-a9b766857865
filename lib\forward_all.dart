import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:smsautoforwardapp/bottomsheet_email.dart';
import 'package:smsautoforwardapp/bottomsheet_url.dart';
import 'package:smsautoforwardapp/controller/rules_controller.dart';
import 'package:smsautoforwardapp/style.dart';

import 'controller/auth_controller.dart';
import 'model/user.dart';

class ForwardAll extends StatefulWidget {
  const ForwardAll({super.key});

  @override
  State<ForwardAll> createState() => _ForwardAllState();
}

class _ForwardAllState extends State<ForwardAll> {
  final AuthController authController = Get.find();
  final RulesController rulesController = RulesController();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: bgColor,
        resizeToAvoidBottomInset: false,
        body: SafeArea(
            child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(children: [
                  // SizedBox(
                  //   height: Get.height * 0.0,
                  // ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Card(
                        elevation: 1,
                        shape: const CircleBorder(),
                        child: Center(
                          child: Padding(
                            padding: const EdgeInsets.all(5.0),
                            child: InkWell(
                              onTap: () {
                                Get.back();
                              },
                              child: const Icon(
                                Icons.arrow_back,
                                color: blackishColor,
                              ),
                            ),
                          ),
                        ),
                      ),
                      txt(
                          txt: 'Forward All',
                          fontWeight: FontWeight.bold,
                          fontColor: blackishColor,
                          fontSize: 26),
                      const IconButton(
                        onPressed: null,
                        icon: Icon(
                          Icons.add_box_outlined,
                          color: bgColor,
                        ),
                      )
                    ],
                  ),
                  SizedBox(
                    height: Get.height * 0.03,
                  ),
                  Expanded(
                      child: StreamBuilder<
                              DocumentSnapshot<Map<String, dynamic>>>(
                          stream: FirebaseFirestore.instance
                              .collection('users')
                              .doc(authController.user!.uid)
                              .snapshots(),
                          builder: (context, snapshot) {
                            if (snapshot.hasData) {
                              final userData =
                                  snapshot.data!.data() as Map<String, dynamic>;
                              final user = UserModel.fromJson(userData);

                              return Padding(
                                padding: const EdgeInsets.all(5.0),
                                child: ListView(children: [
                                  Card(
                                    elevation: 0,
                                    child: ListTile(
                                      onTap: () {
                                        bottomSheetEmail(
                                            context, user.forwardAllEmail!);
                                      },
                                      trailing: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Switch(
                                              value: user
                                                  .forwardAllEmail!.isActive,
                                              onChanged: (bool value) {
                                                rulesController
                                                    .updateForwardAllEmailActiveStatus(
                                                        user.forwardAllEmail!,
                                                        value);
                                              },
                                            ),
                                            const SizedBox(
                                              width: 8,
                                            ),
                                          ]),
                                      title: Row(
                                        children: [
                                          const Icon(
                                            Icons.email_outlined,
                                            color: maincolor,
                                          ),
                                          const SizedBox(
                                            width: 8,
                                          ),
                                          txt(txt: 'To Email', fontSize: 18),
                                        ],
                                      ),
                                    ),
                                  ),
                                  Card(
                                    elevation: 0,
                                    child: ListTile(
                                      onTap: () {
                                        bottomSheetUrl(
                                            context, user.forwardAllUrl!);
                                      },
                                      trailing: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Switch(
                                              value:
                                                  user.forwardAllUrl!.isActive,
                                              onChanged: (bool value) {
                                                rulesController
                                                    .updateForwardAllUrlActiveStatus(
                                                        user.forwardAllUrl!,
                                                        value);
                                              },
                                            ),
                                            const SizedBox(
                                              width: 8,
                                            ),
                                          ]),
                                      title: Row(
                                        children: [
                                          const Icon(
                                            Icons.link,
                                            color: maincolor,
                                          ),
                                          const SizedBox(
                                            width: 8,
                                          ),
                                          txt(txt: 'To URL', fontSize: 18),
                                        ],
                                      ),
                                    ),
                                  )
                                ]),
                              );
                            } else {
                              return Container();
                            }
                          }))
                ]))));
  }
}
