import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:smsautoforwardapp/controller/auth_controller.dart';
import 'package:smsautoforwardapp/controller/rules_controller.dart';
import 'package:smsautoforwardapp/model/logs_model.dart';
import 'package:smsautoforwardapp/style.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:smsautoforwardapp/widgets/premium_ui.dart';

import 'model/user.dart';

class Logs extends StatefulWidget {
  const Logs({super.key});

  @override
  State<Logs> createState() => _LogsState();
}

class _LogsState extends State<Logs> {
  final AuthController authController = Get.find();
  final RulesController rulesController = RulesController();

  bool isDeleting = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: bgColor,
        resizeToAvoidBottomInset: false,
        body: SafeArea(
            child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(children: [
                  // SizedBox(
                  //   height: Get.height * 0.0,
                  // ),

                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Card(
                        color: bgColor,
                        elevation: 0,
                        shape: CircleBorder(),
                        child: Center(
                          child: Padding(
                            padding: EdgeInsets.all(5.0),
                            child: InkWell(
                              onTap: null,
                              child: Icon(
                                Icons.arrow_back,
                                color: bgColor,
                              ),
                            ),
                          ),
                        ),
                      ),
                      txt(
                          txt: 'Logs',
                          fontWeight: FontWeight.bold,
                          fontColor: blackishColor,
                          fontSize: 26),
                      IconButton(
                        icon: const Icon(Icons.delete),
                        onPressed: () async {
                          setState(() {
                            isDeleting = true;
                          });
                          try {
                            await rulesController.deleteAllLogs();
                          } catch (e) {
                            setState(() {
                              isDeleting = false;
                            });
                          }
                          setState(() {
                            isDeleting = false;
                          });
                        },
                      )
                    ],
                  ),
                  SizedBox(
                    height: Get.height * 0.03,
                  ),

                  StreamBuilder<DocumentSnapshot<Map<String, dynamic>>>(
                    stream: FirebaseFirestore.instance
                        .collection('users')
                        .doc(authController.user!.uid)
                        .snapshots(),
                    builder: (context, snapshot) {
                      if (snapshot.data != null) {
                        final userData =
                            snapshot.data!.data() as Map<String, dynamic>;
                        final user = UserModel.fromJson(userData);

                        return Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: user.noOfForwardsused ==
                                        user.noOfForwardsPerMonth ||
                                    user.noOfForwardsused! >
                                        user.noOfForwardsPerMonth!
                                ? Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      txt(
                                          txt:
                                              'Please upgrade subscription if you want more forwards per month.',
                                          maxLines: 10,
                                          fontWeight: FontWeight.w600,
                                          fontColor: maincolor,
                                          fontSize: 24),
                                      ElevatedButton(
                                        onPressed: () {
                                          premiumBottomSheet(
                                              context: context,
                                              isModifyingSubscription: false);
                                        },
                                        child: txt(
                                          txt: 'Click here to upgrade',
                                          fontStyle: FontStyle.italic,
                                          fontSize: 16,
                                          fontColor: Colors.white,
                                        ),
                                      ),
                                    ],
                                  )
                                : Container());
                      } else {
                        return Container();
                      }
                    },
                  ),

                  Expanded(
                      child: StreamBuilder<QuerySnapshot<Map<String, dynamic>>>(
                    stream: FirebaseFirestore.instance
                        .collection('users')
                        .doc(authController.user!.uid)
                        .collection('logs')
                        .orderBy('dateTime',
                            descending:
                                true) // Add this line for ordering by dateTime
                        .snapshots(),
                    builder: (context, snapshot) {
                      if (snapshot.hasError) {
                        return Container();
                      }

                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return const Center(child: CircularProgressIndicator());
                      }

                      final documents = snapshot.data?.docs ?? [];

                      if (documents.isEmpty) {
                        return Center(
                          child: txt(
                            txt: 'No logs yet',
                            fontSize: 16,
                          ),
                        );
                      }
                      if (isDeleting) {
                        return const Center(child: CircularProgressIndicator());
                      }

                      return ListView.builder(
                        itemCount: documents.length,
                        itemBuilder: (context, index) {
                          final docData = documents[index].data();
                          final log = LogModelClass.fromJson(docData);

                          if (log.isEmail) {
                            return Padding(
                              padding: const EdgeInsets.fromLTRB(8, 3, 8, 3),
                              child: Card(
                                  elevation: 0,
                                  child: Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: ListTile(
                                      dense: true,
                                      title: Padding(
                                        padding:
                                            const EdgeInsets.only(bottom: 3),
                                        child: Row(children: [
                                          const Icon(
                                            Icons.email_outlined,
                                            color: maincolor,
                                          ),
                                          const SizedBox(
                                            width: 8,
                                          ),
                                          txt(
                                              txt: ' ${log.ruleName}',
                                              fontSize: 18,
                                              fontWeight: FontWeight.bold),
                                        ]),
                                      ),
                                      subtitle: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Padding(
                                              padding: const EdgeInsets.only(
                                                  bottom: 3),
                                              child: RichText(
                                                maxLines: 20,
                                                text: TextSpan(
                                                  children: [
                                                    TextSpan(
                                                      text: 'The message',
                                                      style:
                                                          GoogleFonts.raleway(
                                                        textStyle:
                                                            const TextStyle(
                                                          color:
                                                              blackishColor, // Change the color as needed
                                                          fontSize: 14,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                        ),
                                                      ),
                                                    ),
                                                    TextSpan(
                                                      text:
                                                          ' \'${log.message}\' ',
                                                      style:
                                                          GoogleFonts.raleway(
                                                        textStyle:
                                                            const TextStyle(
                                                          color:
                                                              maincolor, // Change the color as needed
                                                          fontSize: 14,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                        ),
                                                      ),
                                                    ),
                                                    TextSpan(
                                                      text: log.status
                                                          ? 'has been successfully sent to '
                                                          : 'has been failed to send on this address ',
                                                      style:
                                                          GoogleFonts.raleway(
                                                        textStyle:
                                                            const TextStyle(
                                                          color:
                                                              blackishColor, // Change the color as needed
                                                          fontSize: 14,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                        ),
                                                      ),
                                                    ),
                                                    TextSpan(
                                                      text: log.receiver,
                                                      style:
                                                          GoogleFonts.raleway(
                                                        textStyle:
                                                            const TextStyle(
                                                          color:
                                                              maincolor, // Change the color as needed
                                                          fontSize: 14,

                                                          fontWeight:
                                                              FontWeight.w600,
                                                        ),
                                                      ),
                                                    ),
                                                    TextSpan(
                                                      text:
                                                          ' ${log.additionalMessage}',
                                                      style:
                                                          GoogleFonts.raleway(
                                                        textStyle:
                                                            const TextStyle(
                                                          color:
                                                              blackishColor, // Change the color as needed
                                                          fontSize: 14,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              )),
                                          Padding(
                                            padding: const EdgeInsets.only(
                                                bottom: 3),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.end,
                                              children: [
                                                txt(
                                                    txt: DateFormat(
                                                            'MM/dd, h:mm a')
                                                        .format(log.dateTime)
                                                        .toString(),
                                                    maxLines: 20,
                                                    fontSize: 18),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  )),
                            );
                          } else {
                            return Padding(
                                padding: const EdgeInsets.fromLTRB(8, 3, 8, 3),
                                child: Card(
                                    elevation: 0,
                                    child: Padding(
                                        padding: const EdgeInsets.all(8.0),
                                        child: ListTile(
                                          dense: true,
                                          onTap: () {},
                                          title: Padding(
                                            padding: const EdgeInsets.only(
                                                bottom: 3),
                                            child: Row(children: [
                                              const Icon(
                                                Icons.link,
                                                color: maincolor,
                                              ),
                                              const SizedBox(
                                                width: 8,
                                              ),
                                              txt(
                                                  txt: ' ${log.ruleName}',
                                                  fontSize: 18,
                                                  fontWeight: FontWeight.bold),
                                            ]),
                                          ),
                                          subtitle: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              RichText(
                                                maxLines: 20,
                                                text: TextSpan(
                                                  children: [
                                                    TextSpan(
                                                      text: 'The message',
                                                      style:
                                                          GoogleFonts.raleway(
                                                        textStyle:
                                                            const TextStyle(
                                                          color:
                                                              blackishColor, // Change the color as needed
                                                          fontSize: 14,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                        ),
                                                      ),
                                                    ),
                                                    TextSpan(
                                                      text:
                                                          ' \'${log.message}\' ',
                                                      style:
                                                          GoogleFonts.raleway(
                                                        textStyle:
                                                            const TextStyle(
                                                          color:
                                                              maincolor, // Change the color as needed
                                                          fontSize: 14,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                        ),
                                                      ),
                                                    ),
                                                    TextSpan(
                                                      text: log.status
                                                          ? 'has been successfully sent using the '
                                                          : 'has been failed to send using the ',
                                                      style:
                                                          GoogleFonts.raleway(
                                                        textStyle:
                                                            const TextStyle(
                                                          color:
                                                              blackishColor, // Change the color as needed
                                                          fontSize: 14,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                        ),
                                                      ),
                                                    ),
                                                    TextSpan(
                                                      text: log.httpMethod,
                                                      style:
                                                          GoogleFonts.raleway(
                                                        textStyle:
                                                            const TextStyle(
                                                          color:
                                                              maincolor, // Change the color as needed
                                                          fontSize: 14,

                                                          fontWeight:
                                                              FontWeight.w600,
                                                        ),
                                                      ),
                                                    ),
                                                    TextSpan(
                                                      text:
                                                          ' method to this url \n',
                                                      style:
                                                          GoogleFonts.raleway(
                                                        textStyle:
                                                            const TextStyle(
                                                          color:
                                                              blackishColor, // Change the color as needed
                                                          fontSize: 14,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                        ),
                                                      ),
                                                    ),
                                                    TextSpan(
                                                      text: log.url,
                                                      style:
                                                          GoogleFonts.raleway(
                                                        textStyle:
                                                            const TextStyle(
                                                          color:
                                                              maincolor, // Change the color as needed
                                                          fontSize: 14,

                                                          fontWeight:
                                                              FontWeight.w600,
                                                        ),
                                                      ),
                                                    ),
                                                    TextSpan(
                                                      text:
                                                          ' ${log.additionalMessage}',
                                                      style:
                                                          GoogleFonts.raleway(
                                                        textStyle:
                                                            const TextStyle(
                                                          color:
                                                              blackishColor, // Change the color as needed
                                                          fontSize: 14,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              Padding(
                                                padding: const EdgeInsets.only(
                                                    bottom: 3),
                                                child: Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment.end,
                                                    children: [
                                                      txt(
                                                          txt: DateFormat(
                                                                  'MM/dd, h:mm a')
                                                              .format(
                                                                  log.dateTime)
                                                              .toString(),
                                                          maxLines: 20,
                                                          fontSize: 18),
                                                    ]),
                                              )
                                              // Padding(
                                              //   padding: const EdgeInsets.only(
                                              //       bottom: 3),
                                              //   child: Row(
                                              //     children: [
                                              //       SizedBox(
                                              //         width: Get.width * 0.25,
                                              //         child: txt(
                                              //             txt: 'Rule Name: ',
                                              //             fontWeight:
                                              //                 FontWeight.bold,
                                              //             maxLines: 20,
                                              //             fontSize: 22),
                                              //       ),
                                              //       txt(
                                              //           txt: ' ${log.ruleName}',
                                              //           maxLines: 20,
                                              //           fontSize: 18),
                                              //     ],
                                              //   ),
                                              // ),
                                              // Padding(
                                              //   padding: const EdgeInsets.only(
                                              //       bottom: 3),
                                              //   child: Row(
                                              //     children: [
                                              //       SizedBox(
                                              //         width: Get.width * 0.25,
                                              //         child: txt(
                                              //             txt: 'Keyword: ',
                                              //             fontWeight:
                                              //                 FontWeight.bold,
                                              //             maxLines: 20,
                                              //             fontSize: 22),
                                              //       ),
                                              //       txt(
                                              //           txt: log.keywords
                                              //                   .isEmpty
                                              //               ? 'no defined keywords'
                                              //               : log.keywords
                                              //                   .join(', '),
                                              //           maxLines: 20,
                                              //           fontSize: 18),
                                              //     ],
                                              //   ),
                                              // ),
                                              // Padding(
                                              //   padding: const EdgeInsets.only(
                                              //       bottom: 3),
                                              //   child: Row(
                                              //     children: [
                                              //       SizedBox(
                                              //         width: Get.width * 0.25,
                                              //         child: txt(
                                              //             txt: 'Message: ',
                                              //             fontWeight:
                                              //                 FontWeight.bold,
                                              //             maxLines: 20,
                                              //             fontSize: 22),
                                              //       ),
                                              //       txt(
                                              //           txt: log.message,
                                              //           maxLines: 20,
                                              //           fontSize: 18),
                                              //     ],
                                              //   ),
                                              // ),
                                              // Padding(
                                              //   padding: const EdgeInsets.only(
                                              //       bottom: 3),
                                              //   child: Row(
                                              //     crossAxisAlignment:
                                              //         CrossAxisAlignment.start,
                                              //     children: [
                                              //       SizedBox(
                                              //         width: Get.width * 0.25,
                                              //         child: txt(
                                              //             txt: 'URL: ',
                                              //             fontWeight:
                                              //                 FontWeight.bold,
                                              //             maxLines: 20,
                                              //             fontSize: 22),
                                              //       ),
                                              //       Expanded(
                                              //         child: txt(
                                              //             txt: log.url,
                                              //             maxLines: 20,
                                              //             fontSize: 18),
                                              //       ),
                                              //     ],
                                              //   ),
                                              // ),
                                              // Padding(
                                              //   padding: const EdgeInsets.only(
                                              //       bottom: 3),
                                              //   child: Row(
                                              //     children: [
                                              //       SizedBox(
                                              //         width: Get.width * 0.25,
                                              //         child: txt(
                                              //             txt: 'HttpMethod: ',
                                              //             fontWeight:
                                              //                 FontWeight.bold,
                                              //             maxLines: 20,
                                              //             fontSize: 22),
                                              //       ),
                                              //       txt(
                                              //           txt: log.httpMethod,
                                              //           maxLines: 20,
                                              //           fontSize: 18),
                                              //     ],
                                              //   ),
                                              // ),
                                              // log.httpMethod == 'GET'
                                              //     ? const SizedBox.shrink()
                                              //     : Padding(
                                              //         padding:
                                              //             const EdgeInsets.only(
                                              //                 bottom: 3),
                                              //         child: Row(
                                              //           crossAxisAlignment:
                                              //               CrossAxisAlignment
                                              //                   .start,
                                              //           children: [
                                              //             SizedBox(
                                              //               width: Get.width *
                                              //                   0.25,
                                              //               child: txt(
                                              //                   txt:
                                              //                       'JsonBody: ',
                                              //                   fontWeight:
                                              //                       FontWeight
                                              //                           .bold,
                                              //                   maxLines: 20,
                                              //                   fontSize: 22),
                                              //             ),
                                              //             Expanded(
                                              //               child: txt(
                                              //                   txt: log
                                              //                       .jsonBody,
                                              //                   maxLines: 20,
                                              //                   fontSize: 18),
                                              //             ),
                                              //           ],
                                              //         ),
                                              //       ),
                                              // Padding(
                                              //   padding: const EdgeInsets.only(
                                              //       bottom: 3),
                                              //   child: Row(
                                              //     children: [
                                              //       Expanded(
                                              //         child: Row(
                                              //           children: [
                                              //             SizedBox(
                                              //               width: Get.width *
                                              //                   0.25,
                                              //               child: txt(
                                              //                   txt: 'Status: ',
                                              //                   fontWeight:
                                              //                       FontWeight
                                              //                           .bold,
                                              //                   maxLines: 20,
                                              //                   fontSize: 22),
                                              //             ),
                                              //             txt(
                                              //                 txt: log.status
                                              //                     ? 'Success'
                                              //                     : 'Failed',
                                              //                 maxLines: 20,
                                              //                 fontSize: 18),
                                              //           ],
                                              //         ),
                                              //       ),
                                              //     ],
                                              //   ),
                                              // ),
                                              // Padding(
                                              //   padding: const EdgeInsets.only(
                                              //       bottom: 3),
                                              //   child: Row(
                                              //     mainAxisAlignment:
                                              //         MainAxisAlignment.end,
                                              //     children: [
                                              //       txt(
                                              //           txt: DateFormat(
                                              //                   'MM/dd, h:mm a')
                                              //               .format(
                                              //                   log.dateTime)
                                              //               .toString(),
                                              //           maxLines: 20,
                                              //           fontSize: 18),
                                              //       SizedBox(
                                              //         width: 8,
                                              //       ),
                                              //       txt(
                                              //           txt: log.status
                                              //               ? 'Success'
                                              //               : 'Failed',
                                              //           maxLines: 20,
                                              //           fontSize: 18),
                                              //     ],
                                              //   ),
                                              // ),
                                            ],
                                          ),
                                        ))));
                          }
                        },
                      );
                    },
                  ))
                ]))));
  }
}
