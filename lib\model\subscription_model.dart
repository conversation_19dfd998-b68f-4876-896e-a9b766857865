import 'package:cloud_firestore/cloud_firestore.dart';

class SubsriptionModel {
  String subscriptionID;
  String subscriptionItem;
  String subscriptionPlan;
  String planDescription;

  SubsriptionModel({
    required this.subscriptionID,
    required this.subscriptionItem,
    required this.subscriptionPlan,
    required this.planDescription,
  });

  factory SubsriptionModel.fromJson(Map<String, dynamic> json) {
    return SubsriptionModel(
      subscriptionID: json['subscriptionID'],
      subscriptionItem: json['subscriptionItem'],
      subscriptionPlan: json['subscriptionPlan'],
      planDescription: json['planDescription'],
    );
  }

  Map<String, dynamic> toJson() => {
        "subscriptionID": subscriptionID,
        "subscriptionItem": subscriptionItem,
        "subscriptionPlan": subscriptionPlan,
        "planDescription": planDescription,
      };

  static SubsriptionModel fromSnap(DocumentSnapshot snap) {
    var snapshot = snap.data() as Map<String, dynamic>;
    return SubsriptionModel(
      subscriptionID: snapshot['subscriptionID'],
      subscriptionItem: snapshot['subscriptionItem'],
      subscriptionPlan: snapshot['subscriptionPlan'],
      planDescription: snapshot['planDescription'],
    );
  }
}
