package com.smsautoforward.smsautoforwardapp

import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import android.content.Intent 
import android.util.Log
import com.google.firebase.FirebaseApp
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.android.FlutterFragmentActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugins.GeneratedPluginRegistrant

class MainActivity: FlutterFragmentActivity () {


    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        GeneratedPluginRegistrant.registerWith(flutterEngine)
        // Initialize Firebase (if not already initialized)
        FirebaseApp.initializeApp(this)


        // Register a method channel for starting and stopping the background service
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, "smsAutoForwarderAppChannel")
    .setMethodCallHandler { call, result ->
        when (call.method) {
            "sendAuthStateToAndroid" -> {
                val uid = call.argument<String>("uid")
                if (uid != null) {
                    val prefs: SharedPreferences = this.getSharedPreferences("UUIDSTRING", Context.MODE_PRIVATE)
                    prefs.edit().putString("UUIDSTRING", uid).apply()
                    val uiddd: String? = prefs.getString("UUIDSTRING", null)
                    Log.d(TAG, "Stored 'UUIDSTRING' in Android SharedPreferences: $uiddd")
                    result.success(true)
                } else {
                    result.error("UID_NULL", "UID is null or missing", null)
                }
            }
            "stopBackgroundService" -> {
                val intent = Intent(this, SmsBackgroundReceiver::class.java) // Replace with your actual service class
                stopService(intent)
                result.success(true)
            }
            else -> result.notImplemented()
        }
    }
    }


    companion object {
        private const val TAG = "MainActivity"
    }
}
