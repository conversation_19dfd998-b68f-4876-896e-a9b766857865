// Define a platform channel
import 'package:flutter/services.dart';

const platform = MethodChannel('smsAutoForwarderAppChannel');

// Start the background service from Flutter
Future<void> startBackgroundService() async {
  try {
    await platform.invokeMethod('registerSmsBackgroundReceiver');
  } catch (e) {
    // This is not critical since the receiver is already registered in AndroidManifest.xml
  }
}

Future<void> sendAuthStateToAndroid(String uid) async {
  await platform.invokeMethod('sendAuthStateToAndroid', {'uid': uid});
}

Future<void> stopBackgroundService() async {
  await platform.invokeMethod('stopBackgroundService');
}
