// Define a platform channel
import 'package:flutter/services.dart';

const platform = MethodChannel('smsAutoForwarderAppChannel');

// Start the background service from Flutter
Future<void> startBackgroundService() async {
  await platform.invokeMethod(
    'registerSmsBackgroundReceiver',
  );
}

Future<void> sendAuthStateToAndroid(String uid) async {
  await platform.invokeMethod('sendAuthStateToAndroid', {'uid': uid});
}

Future<void> stopBackgroundService() async {
  print('Flutter: Calling stopBackgroundService...');
  await platform.invokeMethod('stopBackgroundService');
  print('Flutter: stopBackgroundService completed');
}
