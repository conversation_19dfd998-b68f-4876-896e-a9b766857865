import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:smsautoforwardapp/controller/payment_controller.dart';
import 'package:smsautoforwardapp/style.dart';
import 'package:smsautoforwardapp/widgets/premium_ui.dart';

import 'controller/auth_controller.dart';
import 'model/user.dart';

class ManageSubscription extends StatefulWidget {
  const ManageSubscription({super.key});

  @override
  State<ManageSubscription> createState() => _ManageSubscriptionState();
}

class _ManageSubscriptionState extends State<ManageSubscription> {
  final AuthController authController = Get.find();
  final PaymentController paymentController = Get.find();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: bgColor,
      resizeToAvoidBottomInset: false,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Card(
                    elevation: 1,
                    shape: const CircleBorder(),
                    child: Center(
                      child: Padding(
                        padding: const EdgeInsets.all(5.0),
                        child: InkWell(
                          onTap: () {
                            Get.back();
                          },
                          child: const Icon(
                            Icons.arrow_back,
                            color: blackishColor,
                          ),
                        ),
                      ),
                    ),
                  ),
                  txt(
                    txt: 'Manage Subscription',
                    fontWeight: FontWeight.bold,
                    fontColor: blackishColor,
                    fontSize: 26,
                  ),
                  const IconButton(
                    onPressed: null,
                    icon: Icon(
                      Icons.add_box_outlined,
                      color: bgColor,
                    ),
                  ),
                ],
              ),
              Obx(
                () => Expanded(
                  child: paymentController.isModifyingSubscription.isTrue
                      ? const Center(child: CircularProgressIndicator())
                      : StreamBuilder<DocumentSnapshot<Map<String, dynamic>>>(
                          stream: FirebaseFirestore.instance
                              .collection('users')
                              .doc(authController.user!.uid)
                              .snapshots(),
                          builder: (context, snapshot) {
                            if (snapshot.hasError) {
                              return Container();
                            }

                            if (snapshot.connectionState ==
                                ConnectionState.waiting) {
                              return const Center(
                                  child: CircularProgressIndicator());
                            }

                            if (snapshot.data == null) {
                              return Container();
                            }

                            final userData =
                                snapshot.data!.data() as Map<String, dynamic>;
                            final user = UserModel.fromJson(userData);

                            return Container(
                              color: Colors.white,
                              child: Padding(
                                padding: const EdgeInsets.all(20.0),
                                child: Column(
                                  crossAxisAlignment:
                                      CrossAxisAlignment.stretch,
                                  children: [
                                    user.subscriptionEndsAt == null
                                        ? Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              txt(
                                                txt: 'Current Subscribed Plan',
                                                fontSize: 20,
                                                fontWeight: FontWeight.bold,
                                              ),
                                              const SizedBox(height: 10),
                                              txt(
                                                txt: user.currentSubscription
                                                        ?.subscriptionPlan ??
                                                    'Unknown',
                                                fontSize: 18,
                                                fontWeight: FontWeight.bold,
                                              ),
                                              const SizedBox(height: 10),
                                              txt(
                                                txt: user.currentSubscription
                                                        ?.planDescription ??
                                                    'No description',
                                                fontSize: 18,
                                              ),
                                              const SizedBox(height: 10),
                                            ],
                                          )
                                        : Column(
                                            children: [
                                              txt(
                                                txt: user.subscriptionEndsAt !=
                                                        null
                                                    ? 'Subscription will be cancelled on ${DateFormat('dd MMM yyyy').format(user.subscriptionEndsAt!)}'
                                                    : 'No active subscription',
                                                fontSize: 18,
                                              ),
                                              const SizedBox(height: 10),
                                            ],
                                          ),
                                    ElevatedButton(
                                      onPressed: user.subscriptionEndsAt == null
                                          ? () {
                                              cancelSubscriptionConfirmDialog(
                                                context,
                                                user.currentSubscription!
                                                    .subscriptionID,
                                              );
                                            }
                                          : null,
                                      child: txt(
                                        txt: 'Cancel current subscription',
                                        fontSize: 16,
                                        fontColor: Colors.white,
                                      ),
                                    ),
                                    const SizedBox(height: 10),
                                    ElevatedButton(
                                      onPressed: () {
                                        premiumBottomSheet(
                                          context: context,
                                          subscriptionItem: user
                                              .currentSubscription!
                                              .subscriptionItem,
                                          subscriptionId: user
                                              .currentSubscription!
                                              .subscriptionID,
                                          isModifyingSubscription: true,
                                        );
                                      },
                                      child: txt(
                                        txt: 'Upgrade subscription plan',
                                        fontSize: 16,
                                        fontColor: Colors.white,
                                      ),
                                    ),
                                    txt(
                                      txt:
                                          'You can upgrade or downgrade subscription to different plans.',
                                      fontSize: 14,
                                      fontStyle: FontStyle.italic,
                                      maxLines: 30,
                                      fontColor: maincolor,
                                    ),
                                    const SizedBox(height: 10),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

Future<bool?> cancelSubscriptionConfirmDialog(
    BuildContext context, String id) async {
  return showDialog<bool>(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        backgroundColor: Colors.white,
        title: const Text("Cancel Subscription"),
        content:
            const Text("Are you sure you want to cancel the subscription?"),
        actions: [
          TextButton(
            onPressed: () {
              Get.back(); // User chose "No"
            },
            child: const Text("No"),
          ),
          ElevatedButton(
            onPressed: () {
              final PaymentController paymentController = Get.find();
              Get.back(); // User chose "Yes"
              paymentController.cancelSubscription(id);
            },
            child: const Text(
              "Yes",
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      );
    },
  );
}
