import 'package:flutter/material.dart' hide ModalBottomSheetRoute;
import 'package:get/get.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:smsautoforwardapp/controller/rules_controller.dart';
import 'package:smsautoforwardapp/model/forward_all_url_model.dart';
import 'package:smsautoforwardapp/style.dart';

Future<dynamic> bottomSheetUrl(
    BuildContext context, ForwaredAllURLModelClass forwaredAllURLModelClass) {
  return showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    builder: (context) => PopScope(
      onPopInvokedWithResult: (didPop, result) async {
        await showDiscardChangesDialog(context);
      },
      child: Padding(
        padding: MediaQuery.of(context).viewInsets,
        child: SingleChildScrollView(
          controller: ModalScrollController.of(context),
          child: MyBottomSheetUrl(
              forwaredAllURLModelClass: forwaredAllURLModelClass),
        ),
      ),
    ),
  );
}

class MyBottomSheetUrl extends StatefulWidget {
  final ForwaredAllURLModelClass forwaredAllURLModelClass;
  const MyBottomSheetUrl({super.key, required this.forwaredAllURLModelClass});

  @override
  State<MyBottomSheetUrl> createState() => _MyBottomSheetUrlState();
}

class _MyBottomSheetUrlState extends State<MyBottomSheetUrl> {
  bool isActive = true;
  final RulesController rulesController = RulesController();

  String selectedHttpMethod = 'GET';

  TextEditingController urlController = TextEditingController();
  TextEditingController jsonBodyController = TextEditingController();

  String urlError = '';
  String jsonBodyError = '';

  @override
  void dispose() {
    super.dispose();

    urlController.dispose();
    jsonBodyController.dispose();
  }

  @override
  void initState() {
    super.initState();

    urlController.text = widget.forwaredAllURLModelClass.url;
    isActive = widget.forwaredAllURLModelClass.isActive;
    selectedHttpMethod = widget.forwaredAllURLModelClass.method.isEmpty
        ? 'GET'
        : widget.forwaredAllURLModelClass.method;
    jsonBodyController.text = widget.forwaredAllURLModelClass.jsonBody;

    if (jsonBodyController.text.isEmpty) {
      jsonBodyController.text =
          '{"text": "New message from {sender}:{body}"\n}';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              txt(txt: 'Active', fontSize: 16),
              const Spacer(),
              Switch(
                value: isActive,
                onChanged: (bool value) {
                  setState(() {
                    isActive = value;
                  });
                },
              ),
            ],
          ),
          Column(
            children: [
              Card(
                elevation: 4.0,
                child: InputDecorator(
                  decoration:
                      const InputDecoration(border: OutlineInputBorder()),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<String>(
                      value: selectedHttpMethod,
                      isExpanded: true,
                      dropdownColor: Colors.white,
                      underline: Container(),
                      onChanged: (String? newValue) {
                        setState(() {
                          selectedHttpMethod = newValue!;
                        });
                      },
                      items: <String>['GET', 'POST', 'PUT', 'PATCH', 'DELETE']
                          .map((String value) {
                        return DropdownMenuItem<String>(
                          value: value,
                          child: txt(txt: value, fontSize: 18),
                        );
                      }).toList(),
                    ),
                  ),
                ),
              ),
              Card(
                elevation: 4.0,
                child: TextFormField(
                  controller: urlController,
                  decoration: InputDecoration(
                    labelText: 'URL',
                    hintText: 'http://example.com/message',
                    border: InputBorder.none,
                    errorText: urlError.isNotEmpty ? urlError : null,
                  ),
                  onChanged: (value) {
                    setState(() {
                      urlError = ''; // Clear the error message
                    });
                  },
                ),
              ),
              selectedHttpMethod == 'GET'
                  ? const SizedBox.shrink()
                  : Card(
                      elevation: 4.0,
                      child: TextFormField(
                        maxLines: 5,
                        controller: jsonBodyController,
                        decoration: InputDecoration(
                          labelText: 'JSON BODY',
                          hintText:
                              '{\n"text": "New message from {sender}:{body}"\n}',
                          border: InputBorder.none,
                          errorText:
                              jsonBodyError.isNotEmpty ? jsonBodyError : null,
                        ),
                        onChanged: (value) {
                          setState(() {
                            jsonBodyError = ''; // Clear the error message
                          });
                        },
                      ),
                    ),
            ],
          ),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                if (urlController.text.isEmpty ||
                    jsonBodyController.text.isEmpty) {
                  if (urlController.text.isEmpty) {
                    setState(() {
                      urlError = 'URL cannot be empty';
                    });
                    return;
                  }
                  if (jsonBodyController.text.isEmpty) {
                    setState(() {
                      jsonBodyError = 'JSON BODY cannot be empty';
                    });
                    return;
                  }
                } else {
                  Get.back();
                  rulesController.updateForwardAllUrl(ForwaredAllURLModelClass(
                      isActive: isActive,
                      method: selectedHttpMethod,
                      url: urlController.text,
                      jsonBody: jsonBodyController.text));
                }
              },
              child: txt(txt: 'Apply', fontSize: 16, fontColor: Colors.white),
            ),
          ),
        ],
      ),
    );
  }
}

showDiscardChangesDialog(BuildContext context) async {
  return showDialog<bool>(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        backgroundColor: Colors.white,
        title: const Text("Discard Changes?"),
        content:
            const Text("Do you really want to discard your pending changes?"),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(false); // User chose "No"
            },
            child: const Text("No"),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop(true); // User chose "Yes"
            },
            child: const Text(
              "Yes",
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      );
    },
  );
}
