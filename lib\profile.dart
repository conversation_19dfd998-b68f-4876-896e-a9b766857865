import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:smsautoforwardapp/controller/auth_controller.dart';
import 'package:smsautoforwardapp/controller/payment_controller.dart';
import 'package:smsautoforwardapp/manage_subscription.dart';
import 'package:smsautoforwardapp/style.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:smsautoforwardapp/widgets/premium_ui.dart';

import 'model/user.dart';

class Profile extends StatefulWidget {
  const Profile({super.key});

  @override
  State<Profile> createState() => _ProfileState();
}

class _ProfileState extends State<Profile> {
  final AuthController authController = Get.find();
  final PaymentController paymentController = Get.find();
  bool isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: bgColor,
      resizeToAvoidBottomInset: false,
      body: SafeArea(
        child: Column(
          children: [
            SizedBox(height: Get.height * 0.03),
            appName,
            SizedBox(height: Get.height * 0.03),
            isLoading
                ? const Expanded(
                    child: Center(
                        child: CircularProgressIndicator(
                      color: maincolor,
                    )),
                  )
                : Obx(
                    () => Expanded(
                      child: paymentController.isCreatingSubscription.isTrue ||
                              paymentController
                                  .isCancellingSubscription.isTrue ||
                              authController.user == null
                          ? const Center(
                              child: CircularProgressIndicator(
                              color: maincolor,
                            ))
                          : StreamBuilder<
                              DocumentSnapshot<Map<String, dynamic>>>(
                              stream: FirebaseFirestore.instance
                                  .collection('users')
                                  .doc(authController.user!.uid)
                                  .snapshots(),
                              builder: (context, snapshot) {
                                if (snapshot.hasError) {
                                  return const Center(
                                      child: CircularProgressIndicator());
                                }

                                if (!snapshot.hasData) {
                                  return Container();
                                }

                                if (snapshot.data != null) {
                                  final userData = snapshot.data!.data()
                                      as Map<String, dynamic>;
                                  final user = UserModel.fromJson(userData);

                                  return Padding(
                                    padding: const EdgeInsets.all(5.0),
                                    child: ListView(
                                      children: [
                                        Card(
                                          elevation: 0,
                                          child: ListTile(
                                            onTap: null,
                                            title: Row(
                                              children: [
                                                const Icon(Icons.person),
                                                const SizedBox(width: 8),
                                                txt(
                                                  txt: user.name ?? 'Unknown',
                                                  fontSize: 20,
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                        Card(
                                          elevation: 0,
                                          child: ListTile(
                                            onTap: () async {
                                              if (user.isPremium!) {
                                                Get.to(() =>
                                                    const ManageSubscription());
                                              } else {
                                                premiumBottomSheet(
                                                  context: context,
                                                  isModifyingSubscription:
                                                      false,
                                                );
                                              }
                                            },
                                            title: Row(
                                              children: [
                                                const Icon(
                                                    Icons.workspace_premium),
                                                const SizedBox(width: 8),
                                                txt(
                                                  txt: 'Manage subscription',
                                                  fontSize: 20,
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                        Card(
                                          elevation: 0,
                                          child: ListTile(
                                            onTap: () async {
                                              setState(() {
                                                isLoading = true;
                                              });

                                              await authController.signOut();

                                              setState(() {
                                                isLoading = false;
                                              });

                                              // Navigation will be handled by loginRedirect in AuthController
                                            },
                                            title: Row(
                                              children: [
                                                const Icon(Icons.logout),
                                                const SizedBox(width: 8),
                                                txt(
                                                    txt: 'Sign out',
                                                    fontSize: 20),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                }
                                return Container();
                              },
                            ),
                    ),
                  ),
          ],
        ),
      ),
    );
  }
}
