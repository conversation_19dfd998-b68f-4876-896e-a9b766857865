import 'dart:async';
import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:get/get.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:smsautoforwardapp/bnb.dart';
import 'package:smsautoforwardapp/login.dart';
import 'package:smsautoforwardapp/model/forward_all_email_model.dart';
import 'package:smsautoforwardapp/model/forward_all_url_model.dart';
import 'package:smsautoforwardapp/model/subscription_model.dart';
import 'package:url_launcher/url_launcher.dart';
import '../background_service.dart';
import 'package:http/http.dart' as http;
import '../model/user.dart';
import '../style.dart';

class AuthController extends GetxController {
  static AuthController instance = Get.find();
  Rx<bool> isAuthUpdating = false.obs;
  Rx<int> endTime = 0.obs;

  late Rx<User?> _user;
  bool isLoging = false;
  User? get user => _user.value;
  final _auth = FirebaseAuth.instance;
  RxList<UserModel> allUsersList = <UserModel>[].obs;

  Rx<int> isObscure = 1.obs;

  @override
  void onReady() {
    super.onReady();
    _user = Rx<User?>(_auth.currentUser);
    _user.bindStream(_auth.authStateChanges());
    ever(_user, loginRedirect);
  }

  @override
  void onClose() {
    super.onReady();
  }

  loginRedirect(var user) async {
    Timer(Duration(seconds: isLoging ? 0 : 2), () {
      if (_auth.currentUser == null) {
        isLoging = false;
        Get.offAll(() => const Login());
      } else {
        isLoging = true;
        sendAuthStateToAndroid(_auth.currentUser!.uid);
        Get.offAll(() => const BNB());
      }
    });
  }

  void showDeviceLimitDialog() {
    Get.dialog(
      barrierDismissible: false,
      PopScope(
        canPop: false,
        child: AlertDialog(
          title: const Text('Info'),
          content: const Text(
              'Your current subscription plan allows only 1 device support. To use this device, Please logout from previous device.'),
          actions: [
            ElevatedButton(
              child: const Text(
                'Got it',
                style: TextStyle(color: Colors.white),
              ),
              onPressed: () {
                Get.back();
              },
            ),
          ],
        ),
      ),
    );
  }

  void googleLogin() async {
    String errorMessage = '';
    final GoogleSignIn googleSignIn = GoogleSignIn();
    isAuthUpdating.value = true;
    isLoging = true;

    googleSignIn.disconnect();

    try {
      final GoogleSignInAccount? googleSignInAccount =
          await googleSignIn.signIn();
      if (googleSignInAccount != null) {
        final GoogleSignInAuthentication googleAuth =
            await googleSignInAccount.authentication;
        final crendentials = GoogleAuthProvider.credential(
          accessToken: googleAuth.accessToken,
          idToken: googleAuth.idToken,
        );

        final UserCredential userCredential =
            await _auth.signInWithCredential(crendentials);

        User? registereduser = userCredential.user;

        UserModel user = UserModel(
            name: registereduser!.displayName ?? '',
            email: registereduser.email,
            stripeCustomerID: '',
            noOfForwardsused: 0,
            noOfForwardsPerMonth: 50,
            createdAt: Timestamp.fromMillisecondsSinceEpoch(
                DateTime.now().millisecondsSinceEpoch),
            uid: registereduser.uid,
            isPremium: false,
            isAlreadyLoggedIn: false, // Default to false for new users
            currentSubscription: SubsriptionModel(
                subscriptionItem: '',
                subscriptionID: '',
                subscriptionPlan: '',
                planDescription: ''),
            forwardAllEmail:
                ForwaredAllEmailModelClass(isActive: false, recipients: ['']),
            forwardAllUrl: ForwaredAllURLModelClass(
                isActive: false, method: '', url: '', jsonBody: ''));

        final userDocRef =
            firestore.collection('users').doc(registereduser.uid);
        if (!(await userDocRef.get()).exists) {
          await firestore
              .collection('users')
              .doc(registereduser.uid)
              .set(user.toJson());
        }

        // Check subscription plan and login status
        final userDoc =
            await firestore.collection('users').doc(registereduser.uid).get();
        if (userDoc.exists) {
          final userData = userDoc.data() as Map<String, dynamic>;
          final subscriptionPlan =
              userData['currentSubscription']?['subscriptionPlan'] ?? '';
          final isAlreadyLoggedIn = userData['isAlreadyLoggedIn'] ?? false;

          if (subscriptionPlan != 'Elite Plan' && isAlreadyLoggedIn) {
            await stopService();
            await Future.delayed(const Duration(seconds: 3));
            showDeviceLimitDialog();
            return;
          } else if (subscriptionPlan != 'Elite Plan') {
            // If not Elite Plan and not already logged in, set isAlreadyLoggedIn to true
            await firestore
                .collection('users')
                .doc(registereduser.uid)
                .update({'isAlreadyLoggedIn': true});
          }
        }

        getSuccessSnackBar("successfully logged in");
      }
    } on FirebaseAuthException catch (e) {
      switch (e.code) {
        case "account-exists-with-different-credential":
          errorMessage =
              "An account already exists with the same email address but different sign-in credentials.";
          break;
        case "invalid-email":
          errorMessage = "Invalid email";
          break;
        case "network-request-failed":
          errorMessage = "There is no Internet connection";
          break;
        case "email-already-in-use":
          errorMessage = "The account already exists for that email.";
          break;
        case "user-disabled":
          errorMessage = "User is currently disabled";
          break;
        case "user-not-found":
          errorMessage = "User not found";
          break;
        case "wrong-password":
          errorMessage = "Wrong password";
          break;
        default:
          errorMessage = "Login Failed!";
          break;
      }
      getErrorSnackBar(errorMessage);
    } finally {
      isAuthUpdating.value = false;
    }
  }

  void signInWithFacebook() async {
    String errorMessage = '';
    isAuthUpdating.value = true;
    isLoging = true;

    try {
      final LoginResult result = await FacebookAuth.instance
          .login(permissions: (['email', 'public_profile']));
      final token = result.accessToken!.token;

      final graphResponse = await http.get(Uri.parse(
          'https://graph.facebook.com/'
          'v2.12/me?fields=name,first_name,last_name,email&access_token=$token'));

      jsonDecode(graphResponse.body);

      final AuthCredential facebookCredential =
          FacebookAuthProvider.credential(result.accessToken!.token);
      final userCredential =
          await FirebaseAuth.instance.signInWithCredential(facebookCredential);
      User? registereduser = userCredential.user;

      UserModel user = UserModel(
          name: registereduser!.displayName ?? '',
          email: registereduser.email,
          createdAt: Timestamp.fromMillisecondsSinceEpoch(
              DateTime.now().millisecondsSinceEpoch),
          noOfForwardsused: 0,
          noOfForwardsPerMonth: 50,
          stripeCustomerID: '',
          uid: registereduser.uid,
          isPremium: false,
          isAlreadyLoggedIn: false, // Default to false for new users
          currentSubscription: SubsriptionModel(
              subscriptionItem: '',
              subscriptionID: '',
              subscriptionPlan: '',
              planDescription: ''),
          forwardAllEmail:
              ForwaredAllEmailModelClass(isActive: false, recipients: ['']),
          forwardAllUrl: ForwaredAllURLModelClass(
              isActive: false, method: '', url: '', jsonBody: ''));
      final userDocRef = firestore.collection('users').doc(registereduser.uid);
      if (!(await userDocRef.get()).exists) {
        await firestore
            .collection('users')
            .doc(registereduser.uid)
            .set(user.toJson());
      }

      // Check subscription plan and login status
      final userDoc =
          await firestore.collection('users').doc(registereduser.uid).get();
      if (userDoc.exists) {
        final userData = userDoc.data() as Map<String, dynamic>;
        final subscriptionPlan =
            userData['currentSubscription']?['subscriptionPlan'] ?? '';
        final isAlreadyLoggedIn = userData['isAlreadyLoggedIn'] ?? false;

        if (subscriptionPlan != 'Elite Plan' && isAlreadyLoggedIn) {
          await stopService();
          await Future.delayed(const Duration(seconds: 3));
          showDeviceLimitDialog();
        } else if (subscriptionPlan != 'Elite Plan') {
          // If not Elite Plan and not already logged in, set isAlreadyLoggedIn to true
          await firestore
              .collection('users')
              .doc(registereduser.uid)
              .update({'isAlreadyLoggedIn': true});
        }
      }

      getSuccessSnackBar("successfully logged in");
    } on FirebaseAuthException catch (e) {
      switch (e.code) {
        case "account-exists-with-different-credential":
          errorMessage =
              "An account already exists with the same email address but different sign-in credentials.";
          break;
        case "invalid-email":
          errorMessage = "Invalid email";
          break;
        case "network-request-failed":
          errorMessage = "There is no Internet connection";
          break;
        case "email-already-in-use":
          errorMessage = "The account already exists for that email.";
          break;
        case "user-disabled":
          errorMessage = "User is currently disabled";
          break;
        case "user-not-found":
          errorMessage = "User not found";
          break;
        case "wrong-password":
          errorMessage = "Wrong password";
          break;
        default:
          errorMessage = "Login Failed!";
          break;
      }
      getErrorSnackBar(errorMessage);
    } finally {
      isAuthUpdating.value = false;
    }
  }

  updateUserSubscription(
      {required bool isPremiumUser,
      required int noOfForwardsPerMonth,
      required SubsriptionModel subsriptionModel}) async {
    await firestore.collection('users').doc(user!.uid).update({
      'isPremium': isPremiumUser,
      'noOfForwardsPerMonth': noOfForwardsPerMonth,
      'currentSubscription': subsriptionModel.toJson()
    });
  }

  updateSubscriptionEndsAt({required DateTime? subscriptionEndsAt}) async {
    await firestore.collection('users').doc(user!.uid).update({
      'subscriptionEndsAt': subscriptionEndsAt != null
          ? Timestamp.fromDate(subscriptionEndsAt)
          : null,
    });
  }

  updateStripeCustomerId({
    required String id,
  }) async {
    await firestore.collection('users').doc(user!.uid).update({
      'stripeCustomerID': id,
    });
  }

  signOut() async {
    print('Flutter: signOut() called');
    final user = FirebaseAuth.instance.currentUser;

    if (user != null) {
      print('Flutter: Updating Firestore for user: ${user.uid}');
      // Update isAlreadyLoggedIn to false and FCM token to null before sign out
      await FirebaseFirestore.instance.collection('users').doc(user.uid).set({
        'isAlreadyLoggedIn': false,
        'fcmToken': null,
      }, SetOptions(merge: true));
      print('Flutter: Firestore updated successfully');
    }

    print('Flutter: Calling stopService()');
    await stopService();
    print('Flutter: signOut() completed');
  }

  stopService() async {
    print('Flutter: stopService() called');
    print('Flutter: Calling stopBackgroundService()');
    await stopBackgroundService();
    print('Flutter: stopBackgroundService() completed');

    print('Flutter: Calling Firebase signOut()');
    await _auth.signOut();
    print('Flutter: Firebase signOut() completed');

    print('Flutter: Calling Get.deleteAll()');
    await Get.deleteAll();
    print('Flutter: stopService() completed');
  }

  void checkForUpdate() async {
    final info = await PackageInfo.fromPlatform();
    final currentVersion = info.version;

    final doc = await FirebaseFirestore.instance
        .collection('config')
        .doc('version')
        .get();
    final latestVersion = doc['latestVersion'];
    final forceUpdate = doc['forceUpdate'];
    final updateUrl = doc['updateUrl'];
    final updateDialogText = doc['updateDialogText'];

    if (currentVersion != latestVersion && forceUpdate == true) {
      // Only sign out if user is currently logged in
      if (_auth.currentUser != null) {
        await signOut();
        await Future.delayed(const Duration(seconds: 3));
      }
      Get.dialog(
          barrierDismissible: false,
          PopScope(
            canPop: false,
            child: AlertDialog(
              title: const Text('Update Required'),
              content: Text(updateDialogText),
              actions: [
                ElevatedButton(
                  child: const Text(
                    'Update Now',
                    style: TextStyle(color: Colors.white),
                  ),
                  onPressed: () {
                    launchUrl(Uri.parse(updateUrl));
                  },
                ),
              ],
            ),
          ));
    }
  }
}
